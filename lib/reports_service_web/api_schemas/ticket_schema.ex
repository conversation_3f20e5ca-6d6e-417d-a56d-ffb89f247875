defmodule ReportsServiceWeb.ApiSchemas.TicketSchema do
  @moduledoc false
  alias OpenApiSpex.Schema

  def event_demographics_request_params do
    [
      type: [
        in: :query_parameter,
        description: "Type of statistics",
        type: :string,
        example: "age",
        required: false
      ],
      aggregator: [
        in: :query_parameter,
        description: "Aggregator",
        type: :string,
        example: "avg",
        required: true
      ]
    ]
  end

  defmodule TicketsResponse do
    @moduledoc false
    alias OpenApiSpex.Schema

    require OpenApiSpex

    OpenApiSpex.schema(%{
      title: "TicketsResponse",
      description: "Response with a list of all tickets statistics, aggregate by given values",
      type: :object,
      properties: %{
        event_id: %Schema{
          type: :binary_id,
          description: "Event ID"
        },
        title: %Schema{
          type: :string,
          description: "Event title"
        },
        start_date: %Schema{
          type: :date,
          description: "Date the event starts"
        },
        tickets: %Schema{
          type: :list,
          description: "List with time and amount of sold tickets"
        }
      },
      example: %{
        event_id: "9d5d5467-27b7-46be-8dc8-8db6ffe967eb",
        title: "<PERSON><PERSON><PERSON><PERSON><PERSON>ü<PERSON><PERSON>",
        start_date: "2024-05-15T22:00:00Z",
        tickets: [
          %{time: -93, amount: 7},
          %{time: -9, amount: 17},
          %{time: -7, amount: 19},
          %{time: -1, amount: 20}
        ]
      }
    })
  end

  defmodule EventResponse do
    @moduledoc false
    alias OpenApiSpex.Schema

    require OpenApiSpex

    OpenApiSpex.schema(%{
      title: "EventResponse",
      description: "Response with a list events demographics statistics",
      type: :object,
      properties: %{
        event_id: %Schema{
          type: :binary_id,
          description: "Event ID"
        },
        title: %Schema{
          type: :string,
          description: "Event title"
        },
        start_date: %Schema{
          type: :date,
          description: "Date the event starts"
        },
        data: %Schema{
          type: :object,
          description: "List statistics of orders"
        }
      },
      example: %{
        event_id: "9d5d5467-27b7-46be-8dc8-8db6ffe967eb",
        title: "Gänseblümchen",
        start_date: "2024-05-15T22:00:00Z",
        demographics: %{min_age: "0", max_age: "17", average_age: 8}
      }
    })
  end

  defmodule CityResponse do
    @moduledoc false
    alias OpenApiSpex.Schema

    require OpenApiSpex

    OpenApiSpex.schema(%{
      title: "EventResponse",
      description: "Response with a list events demographics statistics",
      type: :object,
      properties: %{
        event_id: %Schema{
          type: :binary_id,
          description: "Event ID"
        },
        title: %Schema{
          type: :string,
          description: "Event title"
        },
        start_date: %Schema{
          type: :date,
          description: "Date the event starts"
        },
        data: %Schema{
          type: :list,
          description: "List of territories and amount of buyers"
        }
      },
      example: %{
        event_id: "9d5d5467-27b7-46be-8dc8-8db6ffe967eb",
        title: "Gänseblümchen",
        start_date: "2024-05-15T22:00:00Z",
        data: [
          %{city: "Aachen", amount: 8}
        ]
      }
    })
  end

  defmodule TicketCountResponse do
    @moduledoc false
    alias OpenApiSpex.Schema

    require OpenApiSpex

    OpenApiSpex.schema(%{
      title: "TicketCountResponse",
      description: "Response with total ticket counts for an event",
      type: :object,
      properties: %{
        total_count: %Schema{
          type: :integer,
          description: "Total number of tickets matching the filter criteria"
        },
        admission_count: %Schema{
          type: :integer,
          description: "Number of admission tickets (admission=true)"
        },
        extras_count: %Schema{
          type: :integer,
          description: "Number of extra tickets (admission=false)"
        }
      },
      example: %{
        total_count: 150,
        admission_count: 120,
        extras_count: 30
      }
    })
  end
end
