defmodule ReportsServiceWeb.TicketController do
  @moduledoc false
  use OpenApiSpex.ControllerSpecs
  use ReportsServiceWeb, :controller
  use Params

  alias ExRBAC.Plug.VerifyAccess
  alias OpenApiSpex.Reference
  alias OpenApiSpex.Schema
  alias ReportsService.Events.Event
  alias ReportsService.Events.EventPermission
  alias ReportsService.Events.ImportedEvent
  alias ReportsService.Events.Organizer
  alias ReportsService.Orders.ImportedOrder
  alias ReportsService.Orders.Ticket
  alias ReportsServiceWeb.ApiSchemas.TicketSchema.TicketCountResponse
  alias ReportsServiceWeb.ApiSchemas.TicketSchema.TicketsResponse
  alias ReportsServiceWeb.ChangesetJSON

  require Logger

  tags(["Tickets"])

  plug VerifyAccess,
       [permission: ["event.admin", "event.edit", "event.view"]]
       when action in [:show_tickets, :count_tickets]

  operation(:show_tickets,
    summary: "Shows the tickets statistic for a given event.",
    responses: %{
      :ok => {"Ticket response", "application/json", TicketsResponse},
      :unauthorized => %Reference{"$ref": "#/components/responses/unauthorized"},
      :forbidden => %Reference{"$ref": "#/components/responses/forbidden"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"}
    }
  )

  # Handle count view: /reports/api/tickets?view=count
  def show_tickets(conn, %{"view" => "count"} = params) do
    user_id = get_user_id(conn)
    user_roles = get_user_roles(conn)

    Logger.debug("Count tickets for user #{inspect(user_id)} with params #{inspect(params)}")

    with {_, %{valid?: true, changes: %{event_id: event_id} = validated_params} = _changeset} <-
           {:params, count_tickets_params(params)},
         {_, true} <-
           {:permission, EventPermission.has_event_permission(user_id, event_id, :EVENT_ADMIN, user_roles)},
         {_, %Event{} = event} <-
           {:event, Event.get(event_id)} do
      counts = Ticket.count_total_tickets(event_id, validated_params)

      conn
      |> put_status(:ok)
      |> render(:count_tickets, %{event: event, counts: counts})
    else
      {:params, changeset} ->
        conn
        |> put_status(:bad_request)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)

      {:event, nil} ->
        conn
        |> put_status(:not_found)
        |> json(%{
          error_code: :unknown_event_id,
          message: "Unknown event_id"
        })

      {:permission, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{
          error_code: :permission_denied,
          message: "No admin rights for this event"
        })
    end
  end

  def show_tickets(conn, %{"aggregator" => aggregator, "event_id" => event_id, "interval" => "day"} = _params) do
    user_id = get_user_id(conn)
    user_roles = get_user_roles(conn)

    Logger.debug(
      "Show tickets with aggregator 'count' for event_id #{inspect(event_id)} with interval 'day' for user #{inspect(user_id)}"
    )

    with {:permission, true} <-
           {:permission, EventPermission.has_event_permission(user_id, event_id, :EVENT_ADMIN, user_roles)},
         {:event, event} when not is_nil(event) <- {:event, Event.get(event_id)} do
      tickets =
        event_id
        |> Ticket.count_sold_tickets("day", nil, true)
        |> Enum.reduce([], fn ticket, acc ->
          [%{time: Timex.diff(ticket.period, event.start_date, :days), count: ticket.count_tickets} | acc]
        end)

      conn
      |> put_status(:ok)
      |> render(:show_tickets, %{aggregator: String.to_atom(aggregator), event: event, tickets: tickets})
    else
      {:event, nil} ->
        conn
        |> put_status(:not_found)
        |> json(%{
          error_code: :unknown_event_id,
          message: "Unknown event_id"
        })

      {:permission, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{
          error_code: :permission_denied,
          message: "No admin rights for this event"
        })

      error ->
        Logger.error("Can't show tickets for the imported event because of #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          error_code: :unknown_error,
          message: "Can't show the tickets"
        })
    end
  end

  def show_tickets(
        conn,
        %{
          "aggregator" => aggregator,
          "imported_event_id" => imported_event_id,
          "interval" => "day",
          "current_event_id" => current_event_id
        } = _params
      ) do
    user_id = get_user_id(conn)
    user_roles = get_user_roles(conn)

    Logger.debug(
      "Show tickets with aggregator 'count' for imported_event_id #{inspect(imported_event_id)} with interval 'day' for user #{inspect(user_id)}}"
    )

    with {:permission, true} <-
           {:permission, EventPermission.has_event_permission(user_id, current_event_id, :EVENT_ADMIN, user_roles)},
         {:imported_event_id, {:ok, _uuid}} <- {:imported_event_id, Ecto.UUID.cast(imported_event_id)},
         {:imported_event, imported_event} when not is_nil(imported_event) <-
           {:imported_event, ImportedEvent.get(imported_event_id)} do
      tickets =
        imported_event_id
        |> ImportedOrder.count_sold_tickets_by_imported_event_id()
        |> Enum.reduce([], fn ticket, acc ->
          [%{time: Timex.diff(ticket.period, imported_event.start_date, :days), count: ticket.count_tickets} | acc]
        end)

      conn
      |> put_status(:ok)
      |> render(:show_tickets, %{aggregator: String.to_atom(aggregator), event: imported_event, tickets: tickets})
    else
      {:permission, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{
          error_code: :permission_denied,
          message: "No admin rights for this event"
        })

      {:imported_event_id, _error} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error_code: :no_valid_id,
          message: "The given imported_event_id is not valid UUID."
        })

      {:imported_event, nil} ->
        conn
        |> put_status(:not_found)
        |> json(%{
          error_code: :imported_event_not_found,
          message: "No valid imported_event_id"
        })

      error ->
        Logger.error("Can't show tickets for the imported event because of #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          error_code: :unknown_error,
          message: "Can't show the tickets"
        })
    end
  end

  def show_tickets(
        conn,
        %{"aggregator" => aggregator, "imported_event_id" => imported_event_id, "interval" => "day"} = _params
      ) do
    Logger.debug(
      "Show tickets with aggregator 'count' for imported_event_id #{inspect(imported_event_id)} with interval 'day'}"
    )

    with {:user, user_id} when not is_nil(user_id) <- {:user, get_user_id(conn)},
         {:organizer, organizer} when not is_nil(organizer) <-
           {:organizer, Organizer.get_organizer_by_creator(user_id)},
         {:imported_event, imported_event} when not is_nil(imported_event) <-
           {:imported_event, ImportedEvent.get(imported_event_id)} do
      if imported_event.organizer_id == organizer.id do
        tickets =
          imported_event_id
          |> ImportedOrder.count_sold_tickets_by_imported_event_id()
          |> Enum.reduce([], fn ticket, acc ->
            [%{time: Timex.diff(ticket.period, imported_event.start_date, :days), count: ticket.count_tickets} | acc]
          end)

        conn
        |> put_status(:ok)
        |> render(:show_tickets, %{aggregator: String.to_atom(aggregator), event: imported_event, tickets: tickets})
      else
        conn
        |> put_status(:forbidden)
        |> json(%{
          error_code: :permission_denied,
          message: "Wrong promoter"
        })
      end
    else
      {:organizer, nil} ->
        conn
        |> put_status(:forbidden)
        |> json(%{
          error_code: :forbidden,
          message: "Only logged in promoter are allowed to import an event!"
        })

      error ->
        Logger.error("Can't show tickets for the imported event because of #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          error_code: :unknown_error,
          message: "Can't show the tickets"
        })
    end
  end

  operation(:count_tickets,
    summary: "Returns the total count of tickets for a given event with filtering options.",
    parameters: [
      event_id: [
        in: :query,
        description: "Event ID to count tickets for",
        type: :string,
        required: true
      ],
      status: [
        in: :query,
        description: "Filter by specific ticket statuses",
        schema: %Schema{
          type: :array,
          enum: Enum.map(Ticket.ticket_status(), &Atom.to_string/1)
        },
        required: false
      ]
    ],
    responses: %{
      :ok => {"Ticket count response", "application/json", TicketCountResponse},
      :unauthorized => %Reference{"$ref": "#/components/responses/unauthorized"},
      :forbidden => %Reference{"$ref": "#/components/responses/forbidden"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"}
    }
  )

  defparams(
    count_tickets_params(%{
      event_id!: [field: Ecto.UUID],
      status: [field: {:array, Ecto.Enum}, values: Ticket.ticket_status()]
    })
  )

  def count_tickets(conn, params) do
    user_id = get_user_id(conn)
    user_roles = get_user_roles(conn)

    Logger.debug("Count tickets for user #{inspect(user_id)} with params #{inspect(params)}")

    with {_, %{valid?: true, changes: %{event_id: event_id} = validated_params} = _changeset} <-
           {:params, count_tickets_params(params)},
         {_, true} <-
           {:permission, EventPermission.has_event_permission(user_id, event_id, :EVENT_ADMIN, user_roles)},
         {_, %Event{} = event} <-
           {:event, Event.get(event_id)} do
      counts = Ticket.count_total_tickets(event_id, validated_params)

      conn
      |> put_status(:ok)
      |> render(:count_tickets, %{event: event, counts: counts})
    else
      {:params, changeset} ->
        conn
        |> put_status(:bad_request)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)

      {:event, nil} ->
        conn
        |> put_status(:not_found)
        |> json(%{
          error_code: :unknown_event_id,
          message: "Unknown event_id"
        })

      {:permission, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{
          error_code: :permission_denied,
          message: "No admin rights for this event"
        })
    end
  end
end
