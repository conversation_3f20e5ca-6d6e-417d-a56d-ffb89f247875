defmodule ReportsServiceWeb.TicketJSON do
  @moduledoc false

  def show_tickets(%{aggregator: aggregator, event: event, tickets: tickets}) do
    tickets =
      tickets
      |> fill_tickets(event.start_date)
      |> prepare_tickets(aggregator)

    tickets_list = for ticket <- tickets, do: show_ticket_data(ticket, aggregator)

    %{
      event_id: event.id,
      title: event.title,
      start_date: event.start_date,
      tickets: tickets_list
    }
  end

  def show_ticket_data(ticket, :count) do
    %{
      time: ticket.time,
      amount: ticket.count
    }
  end

  def show_ticket_data(ticket, :sum) do
    %{
      time: ticket.time,
      amount: ticket.sum
    }
  end

  def count_tickets(%{counts: counts}) do
    %{
      total_count: counts.total_count,
      admission_count: counts.admission_count,
      extras_count: counts.extras_count
    }
  end

  def prepare_tickets(tickets, :sum) do
    Enum.reduce(tickets, [], fn ticket, acc ->
      case ticket.count do
        nil ->
          [%{time: ticket.time, sum: nil} | acc]

        _count ->
          [
            %{
              time: ticket.time,
              sum: calculate_total_ticket_sum(tickets, ticket)
            }
            | acc
          ]
      end
    end)
  end

  def prepare_tickets(tickets, _aggregator), do: tickets

  defp fill_tickets(nil, _start_date), do: []
  defp fill_tickets([], _start_date), do: []

  defp fill_tickets(tickets, start_date) do
    days_between_event_start_and_first_ticket_sold = Enum.min_by(tickets, &Map.get(&1, :time))
    days_between_event_starts_and_now = Timex.diff(NaiveDateTime.utc_now(), start_date, :days)

    tickets_until_now =
      for i <-
            days_between_event_start_and_first_ticket_sold.time..min(
              days_between_event_starts_and_now,
              0
            ) do
        %{
          time: i,
          count: Enum.find(tickets, %{count: 0}, fn t -> t.time == i end).count
        }
      end

    List.flatten([tickets_until_now | fill_future_tickets(days_between_event_starts_and_now)])
  end

  defp fill_future_tickets(days_between_event_starts_and_now) when days_between_event_starts_and_now >= 0, do: []

  defp fill_future_tickets(days_between_event_starts_and_now) do
    for i <- (days_between_event_starts_and_now + 1)..0 do
      %{
        time: i,
        count: nil
      }
    end
  end

  defp calculate_total_ticket_sum(tickets, ticket),
    do: tickets |> Enum.filter(fn t -> t.time <= ticket.time end) |> Enum.map(& &1.count) |> Enum.sum()
end
